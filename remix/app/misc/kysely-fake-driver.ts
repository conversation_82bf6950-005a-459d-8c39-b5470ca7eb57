import {
  DatabaseConnection,
  Driver,
  QueryResult,
  TransactionSettings,
  CompiledQuery,
  PostgresQueryCompiler,
  PostgresAdapter,
  PostgresIntrospector,
} from "kysely";

/**
 * A fake PostgreSQL driver for Kysely that can be used on the client-side
 * to generate SQL queries without actually connecting to a database.
 * 
 * This is useful when you want to use Kysely's query builder to generate
 * SQL strings for display or other purposes without needing a real database connection.
 */
export class FakePostgresDriver implements Driver {
  async init(): Promise<void> {
    // No initialization needed for fake driver
  }

  async acquireConnection(): Promise<FakePostgresConnection> {
    return new FakePostgresConnection();
  }

  async beginTransaction(
    connection: FakePostgresConnection,
    settings: TransactionSettings
  ): Promise<void> {
    // No-op for fake driver
  }

  async commitTransaction(connection: FakePostgresConnection): Promise<void> {
    // No-op for fake driver
  }

  async rollbackTransaction(connection: FakePostgresConnection): Promise<void> {
    // No-op for fake driver
  }

  async releaseConnection(connection: FakePostgresConnection): Promise<void> {
    // No-op for fake driver
  }

  async destroy(): Promise<void> {
    // No-op for fake driver
  }
}

class FakePostgresConnection implements DatabaseConnection {
  async executeQuery<O>(compiledQuery: CompiledQuery): Promise<QueryResult<O>> {
    // For a fake driver, we don't actually execute the query
    // We just return an empty result set
    // The main purpose is to allow SQL generation via compiledQuery.sql
    
    console.log('Generated SQL:', compiledQuery.sql);
    console.log('Parameters:', compiledQuery.parameters);
    
    return {
      rows: [] as O[],
      numAffectedRows: BigInt(0),
      insertId: undefined,
      numChangedRows: undefined,
    };
  }

  async *streamQuery<O>(
    compiledQuery: CompiledQuery,
    chunkSize?: number
  ): AsyncIterableIterator<QueryResult<O>> {
    // For streaming, just yield an empty result
    yield {
      rows: [] as O[],
      numAffectedRows: BigInt(0),
      insertId: undefined,
      numChangedRows: undefined,
    };
  }
}

/**
 * A fake PostgreSQL dialect that uses the FakePostgresDriver
 */
export class FakePostgresDialect {
  constructor() {}

  createDriver(): Driver {
    return new FakePostgresDriver();
  }

  createQueryCompiler() {
    return new PostgresQueryCompiler();
  }

  createAdapter() {
    return new PostgresAdapter();
  }

  createIntrospector(db: any) {
    return new PostgresIntrospector(db);
  }
}

/**
 * Utility function to generate SQL from a Kysely query without executing it
 */
export async function generateSQL<T>(query: any): Promise<{ sql: string; parameters: readonly unknown[] }> {
  const compiledQuery = query.compile();
  return {
    sql: compiledQuery.sql,
    parameters: compiledQuery.parameters
  };
}

/**
 * Helper function to convert parameterized SQL to raw SQL with embedded values
 */
export function interpolateSQL(sql: string, parameters: readonly unknown[]): string {
  let result = sql;

  // Replace $1, $2, etc. with actual values
  result = result.replace(/\$(\d+)/g, (match, num) => {
    const index = parseInt(num) - 1;
    if (index < parameters.length) {
      const value = parameters[index];

      // Handle different types of values
      if (value === null || value === undefined) {
        return 'NULL';
      } else if (typeof value === 'string') {
        // Escape single quotes and wrap in quotes
        return `'${value.replace(/'/g, "''")}'`;
      } else if (typeof value === 'number' || typeof value === 'bigint') {
        return String(value);
      } else if (typeof value === 'boolean') {
        return value ? 'TRUE' : 'FALSE';
      } else if (value instanceof Date) {
        return `'${value.toISOString()}'`;
      } else if (Array.isArray(value)) {
        // Handle arrays (for PostgreSQL array syntax)
        const arrayValues = value.map(v =>
          typeof v === 'string' ? `"${v.replace(/"/g, '\\"')}"` : String(v)
        ).join(',');
        return `ARRAY[${arrayValues}]`;
      } else {
        // For objects, convert to JSON
        return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
      }
    }
    return match;
  });

  return result;
}

/**
 * Helper function to generate raw SQL from a Kysely query with embedded values
 */
export async function generateRawSQL<T>(query: any): Promise<string> {
  const { sql, parameters } = await generateSQL(query);
  return interpolateSQL(sql, parameters);
}
